# Autocorner - Website
A comprehensive automotive website platform built for Autocorner's Swiss dealership network, specializing in Audi, Skoda, and premium pre-owned vehicles. Built on modern web technologies with integrated AutoScout24 API and advanced content management capabilities.

![Autocorner Platform](doc/autocorner/img.png)

## 🚗 About Autocorner

Autocorner represents 35+ years of automotive excellence in Switzerland, operating multiple centers across the region. Our platform serves as the digital hub for:

- **Audi Dealership Operations** - New and certified pre-owned Audi vehicles
- **Skoda Dealership Services** - Complete Skoda vehicle lineup and services
- **Premium Used Cars** - Curated selection of high-quality pre-owned vehicles
- **Multi-Center Management** - Centralized platform for all Autocorner locations

## 🛠 Tech Stack

### Core Framework
- **[Next.js 15](https://nextjs.org/)** - React framework with App Router and Server Components
- **[TypeScript](https://www.typescriptlang.org/)** - Type-safe development
- **[tRPC](https://trpc.io/)** - End-to-end typesafe APIs
- **[TanStack Query](https://tanstack.com/query)** - Powerful data synchronization

### Content Management
- **[Sanity CMS](https://sanity.io/)** - Headless content management with live preview
- **[Sanity Studio](https://sanity.io/studio)** - Visual content editing with custom schemas
- **Visual Editing** - Live preview and inline editing capabilities

### UI & Styling
- **[Tailwind CSS](https://tailwindcss.com/)** - Utility-first CSS framework
- **[Shadcn/ui](https://ui.shadcn.com/)** - Modern component library
- **[Radix UI](https://www.radix-ui.com/)** - Accessible component primitives
- **[Framer Motion](https://www.framer.com/motion/)** - Animation library

### Data & API Integration
- **[AutoScout24 API](https://www.autoscout24.ch/)** - Vehicle inventory integration
- **[OpenAPI](https://swagger.io/specification/)** - API specification and type generation
- **[Zod](https://zod.dev/)** - Schema validation and type inference

### Development & Tools
- **[Turbopack](https://turbo.build/pack)** - Fast bundler for development
- **[ESLint](https://eslint.org/)** - Code linting and formatting
- **[Prettier](https://prettier.io/)** - Code formatting

## 🎯 Key Features

### 🏢 Multi-Center Management
- **Center-specific content** - Tailored information for each location
- **Staff management** - Team profiles and contact information
- **Service offerings** - Location-specific services and specializations
- **Contact integration** - Unified communication across all centers

### 🚙 Vehicle Management System
- **AutoScout24 Integration** - Real-time vehicle inventory synchronization
- **Advanced Search** - Comprehensive filtering and search capabilities
- **Vehicle Showcases** - Interactive galleries and detailed presentations
- **Brand-specific Pages** - Dedicated Audi and Skoda landing pages

### 📄 Advanced Page Builder
- **12+ Pre-built Blocks** - Professional content blocks ready to use
- **Custom Automotive Blocks** - Vehicle-specific components and layouts
- **Form Builder** - Contact forms, test drive bookings, and inquiries
- **Content Grids** - Flexible layouts for vehicles, news, and services

### 🔧 Developer Features
- **Type Safety** - End-to-end TypeScript implementation
- **API Documentation** - Auto-generated OpenAPI specifications
- **Performance Optimized** - Server-side rendering and caching
- **SEO Ready** - Structured data and meta optimization

## 🚀 Getting Started

### Prerequisites

- **Node.js** (version 18 or higher)
- **pnpm** (recommended package manager)
- **Sanity Account** - For content management

### 1. Environment Setup

Create a `.env.local` file in the project root:

```bash
# Site Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SITE_NAME=Autocorner

# Sanity Configuration
NEXT_PUBLIC_SANITY_DATASET=production
NEXT_PUBLIC_SANITY_PROJECT_ID=your_project_id
NEXT_PUBLIC_SANITY_API_VERSION=2023-05-03
SANITY_API_READ_TOKEN=your_api_token

# Email Configuration (Resend)
RESEND_SENDER_EMAIL=<EMAIL>
RESEND_RECEIVER_EMAIL=<EMAIL>
RESEND_API_KEY=your_resend_api_key

# AutoScout24 API (if applicable)
AUTOSCOUT24_API_KEY=your_autoscout24_api_key
```

### 2. Installation

```bash
# Install dependencies
pnpm install

# Generate API types (if using AutoScout24 integration)
pnpm run generate:types

# Import demo content (optional)
pnpm exec sanity dataset import demo-content.tar.gz production
```

### 3. Development

```bash
# Start development server
pnpm run dev

# Generate type definitions
pnpm run typegen

# Start Sanity Studio
pnpm run dev
# Navigate to http://localhost:3000/studio
```

## 📚 Project Structure

```
src/
├── app/                     # Next.js App Router pages
│   ├── (frontend)/         # Public website pages
│   ├── (backend)/          # Admin and API routes
│   └── studio/             # Sanity Studio integration
├── components/             # React components
│   ├── centers/           # Center-specific components
│   ├── vehicles/          # Vehicle-related components
│   ├── page-builder/      # CMS block components
│   └── shared/            # Reusable UI components
├── lib/                   # Utility functions and configurations
│   ├── api/              # API integration (AutoScout24)
│   ├── sanity/           # Sanity client and queries
│   └── trpc/             # tRPC router and procedures
├── sanity/               # Sanity schemas and configuration
└── types/                # TypeScript type definitions
```

## 🔧 Available Scripts

| Command | Description |
|---------|-------------|
| `pnpm run dev` | Start development server with Turbopack |
| `pnpm run build` | Build production application |
| `pnpm run start` | Start production server |
| `pnpm run typegen` | Generate Sanity TypeScript types |
| `pnpm run generate:types` | Generate API types from OpenAPI specs |
| `pnpm run lint` | Run ESLint code analysis |
| `pnpm run prettier` | Format code with Prettier |

## 🎨 Content Management

### Sanity Studio Features
- **Visual Page Builder** - Drag-and-drop content creation
- **Live Preview** - Real-time content preview
- **Multi-language Support** - French content management
- **Media Management** - Image and video handling
- **Custom Schemas** - Vehicle, center, and content types

### Available Content Types
- **Pages** - Standard website pages with page builder
- **Centers** - Dealership location information
- **Vehicles** - Car inventory and specifications
- **Blog Posts** - News and automotive content
- **Team Members** - Staff profiles and information

## 🚗 AutoScout24 Integration

The platform includes comprehensive AutoScout24 API integration for:

- **Vehicle Inventory** - Real-time synchronization
- **Search & Filtering** - Advanced vehicle search
- **Dealer Management** - Multi-center inventory handling
- **Type Safety** - Generated TypeScript definitions

## 🌐 Deployment

### Recommended Deployment Platforms

- **[Vercel](https://vercel.com/)** - Optimized for Next.js applications
- **[Netlify](https://www.netlify.com/)** - Alternative deployment option
- **Custom Server** - Docker-based deployment available

### Environment Variables

Ensure all environment variables are properly configured in your deployment platform. Reference the `.env.local` example above.

## 📖 Documentation

- **API Integration** - `/doc/autoscout/` - AutoScout24 API documentation
- **Content Strategy** - `/doc/Autocorner/` - Content and marketing guidelines
- **Implementation Guides** - `/doc/llm/` - Technical implementation details

## 🤝 Contributing

This project follows modern development practices:

1. **Type Safety** - All code must be properly typed
2. **Component Architecture** - Reusable and composable components
3. **Performance** - Optimized for speed and user experience
4. **Accessibility** - WCAG compliant interface design

## 👨‍💻 Author

**rickylabs** - Lead Developer  
**[Devocracy](https://www.devocracy.ch)** - Development Agency

---

*Built with passion for automotive excellence and modern web technologies.*
