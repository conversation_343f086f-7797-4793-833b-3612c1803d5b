import { defineConfig } from "sanity";
import { schema } from "./src/sanity/schemas";
import { media } from "sanity-plugin-media";
import { visionTool } from "@sanity/vision";
import { structureTool } from "sanity/structure";
import { structure } from "./src/sanity/lib/structure";
import { presentationTool } from "sanity/presentation";
import { resolve } from "./src/sanity/presentation/resolve";
import { simplerColorInput } from "sanity-plugin-simpler-color-input";
import { defaultDocumentNode } from "./src/sanity/lib/structure/default-document-node";
import {
  apiVersion,
  dataset,
  projectId,
  studioUrl,
  useCdn,
} from "./src/sanity/lib/api";

// Import Sanity Studio UI localization plugins
import { frFRLocale } from "@sanity/locale-fr-fr";
import { getStudioLanguageConfig } from "./src/sanity/dictionary/studio";
import { muxInput } from "sanity-plugin-mux-input";

// Get language configuration
const { defaultLanguage, supportedLanguages } = getStudioLanguageConfig();

const config = defineConfig({
  title: process.env.NEXT_PUBLIC_SITE_NAME,
  useCdn: useCdn,
  dataset: dataset,
  basePath: studioUrl,
  projectId: projectId,
  apiVersion: apiVersion,
  plugins: [
    structureTool({
      structure,
      defaultDocumentNode,
    }),
    presentationTool({
      resolve,
      previewUrl: {
        previewMode: {
          enable: "/api/draft-mode/enable",
          disable: "/api/draft-mode/disable",
        },
      },
    }),
    media(),
    visionTool(),
    simplerColorInput(),
    // Mux Video Input Plugin with enhanced configuration
    muxInput({
      // Enable static MP4 renditions for legacy support
      mp4_support: "standard",
    }),
    // Add Studio UI localization based on configuration
    // Note: English is the default and doesn't need a plugin
    ...(supportedLanguages.includes("fr" as any)
      ? [
          frFRLocale({
            title: "Français",
          }),
        ]
      : []),
  ],
  schema: schema,
});

export default config;
