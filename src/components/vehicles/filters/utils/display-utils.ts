import type { FormattedFilterValue } from "../types";
import type { ListingQuery } from "@/lib/api/autoscout/schemas/generated";

// Define types for AutoScout-specific objects
interface MakeModelVersionObject {
  makeKey?: string;
  modelKey?: string;
  modelGroupKey?: string;
}

interface GeoLocationObject {
  id?: string | number;
  name?: string;
}

type FilterValueType =
  | string
  | number
  | boolean
  | MakeModelVersionObject
  | GeoLocationObject
  | Record<string, unknown>
  | FilterValueType[];

/**
 * Formats filter values for display in filter chips
 * Handles different data types from AutoScout API schema
 */
export function formatFilterValue(filterValue: unknown): string | null {
  if (!filterValue) return null;

  if (Array.isArray(filterValue)) {
    if (filterValue.length === 0) return null;

    // Handle array of objects (like makeModelVersions)
    if (filterValue.length > 0 && typeof filterValue[0] === "object") {
      const objectDescriptions = filterValue.map((obj) =>
        formatObjectValue(obj),
      );

      return objectDescriptions.length > 2
        ? `${objectDescriptions.slice(0, 2).join(", ")}... (+${objectDescriptions.length - 2})`
        : objectDescriptions.join(", ");
    }

    // Handle array of primitives
    return filterValue.length > 2
      ? `${filterValue.slice(0, 2).join(", ")}... (+${filterValue.length - 2})`
      : filterValue.join(", ");
  }

  // Handle single objects
  if (typeof filterValue === "object" && filterValue !== null) {
    return formatObjectValue(filterValue);
  }

  // Handle primitives
  return String(filterValue);
}

/**
 * Formats object values based on their structure
 * Recognizes AutoScout-specific object patterns
 */
function formatObjectValue(obj: Record<string, unknown>): string {
  // Handle makeModelVersions objects
  if (typeof obj.makeKey === 'string' && typeof obj.modelKey === 'string') {
    return typeof obj.modelGroupKey === 'string'
      ? `${obj.makeKey}/${obj.modelGroupKey}/${obj.modelKey}`
      : `${obj.makeKey}/${obj.modelKey}`;
  }

  // Handle geoLocation objects
  if (obj.id || obj.name) {
    return [obj.name, obj.id].filter(Boolean).join(" ");
  }

  // Handle other object types by showing key properties
  const keyProps = Object.entries(obj)
    .filter(([, v]) => v != null)
    .slice(0, 2) // Limit to first 2 properties
    .map(([k, v]) => `${k}:${v}`)
    .join(", ");

  return keyProps || "Object";
}

/**
 * Formats filter key names for display
 * Converts camelCase to readable format
 */
export function formatFilterKey(key: string): string {
  return key
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase())
    .replace(/From$/, " Min")
    .replace(/To$/, " Max");
}

/**
 * Creates formatted filter value objects for display
 */
export function createFormattedFilters(
  queryFilters: Partial<ListingQuery>,
): FormattedFilterValue[] {
  return Object.entries(queryFilters)
    .map(([key, value]) => {
      const displayValue = formatFilterValue(value);
      if (!displayValue) return null;

      return {
        displayKey: formatFilterKey(key),
        displayValue,
        originalKey: key,
        originalValue: value,
      };
    })
    .filter((item): item is FormattedFilterValue => item !== null);
}
