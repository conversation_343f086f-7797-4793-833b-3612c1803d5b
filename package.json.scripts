{
  "scripts": {
    "dev": "next dev",
    "build": "npm run generate:types && next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    
    // AutoScout24 API Integration
    "generate:types": "tsx scripts/generate-types.ts",
    "generate:schemas": "tsx scripts/generate-schemas.ts",
    "validate:openapi": "tsx scripts/validate-openapi.ts",
    
    // Development utilities
    "dev:generate": "npm run generate:types && npm run dev",
    "test:schemas": "vitest src/lib/api/schemas/**/*.test.ts",
    "test:integration": "vitest src/lib/api/integration/**/*.test.ts",
    
    // Production utilities
    "build:check": "npm run generate:types && npm run type-check && npm run build",
    "postinstall": "npm run generate:types"
  },
  
  "devDependencies": {
    "@types/node": "^22.0.0",
    "tsx": "^4.7.0",
    "openapi-typescript": "^7.0.0",
    "yaml": "^2.3.4",
    "vitest": "^1.6.0"
  },
  
  "dependencies": {
    "zod": "^3.25.0",
    "next": "15.1.3",
    "@trpc/server": "next",
    "@trpc/client": "next", 
    "@trpc/tanstack-react-query": "next",
    "@tanstack/react-query": "^5.0.0",
    "superjson": "^2.2.1"
  }
}
