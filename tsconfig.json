{
  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    // Add performance optimizations
    "assumeChangesOnlyAffectDirectDependencies": true,
    "disableReferencedProjectLoad": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"]
    },
    "target": "ES2017"
  },
  "include": [
    "video.d.ts",
    "next-env.d.ts",
    "src/**/*.ts",
    "src/**/*.tsx",
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    // Exclude generated and problematic files from type checking
    "src/lib/api/autoscout/schemas/generated.ts",
    "src/lib/api/autoscout/types/generated.ts",
    "src/lib/api/autoscout/types/api.ts",
    "sanity.types.ts",
    "trace_output",
    "**/*.test.ts",
    "**/*.test.tsx"
  ]
}
